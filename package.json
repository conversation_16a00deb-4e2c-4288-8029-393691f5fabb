{"name": "hifnf", "version": "0.1.0", "private": true, "scripts": {"dev": "node server.js", "dev:next": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "setup-admin": "npm run add-admin-tables && npm run db:generate && npm run db:push", "setup-db": "tsx src/scripts/setup-db.ts", "setup-cpanel-db": "tsx src/scripts/cpanel-db-setup.ts", "add-username": "tsx src/scripts/add-username-column.ts", "generate-usernames": "tsx src/scripts/generate-usernames.ts", "migrate-likes": "tsx src/scripts/migrate-likes.ts", "update-posts": "tsx src/scripts/update-posts-table.ts", "remove-pages": "tsx src/scripts/remove-pages-tables.ts", "add-groups": "tsx src/scripts/add-groups-tables.ts", "add-rules": "tsx src/scripts/add-rules-column.ts", "add-fan-pages": "tsx src/scripts/add-fan-pages-tables.ts", "add-post-permission": "tsx src/scripts/add-post-permission-column.ts", "add-marketplace": "tsx src/scripts/add-marketplace-tables.ts", "add-products": "tsx src/scripts/add-products-table.ts", "fix-collations": "tsx src/scripts/fix-collations.ts", "fix-posts-groups": "tsx src/scripts/fix-posts-groups-collation.ts", "fix-users-groups": "tsx src/scripts/fix-users-groups-collation.ts", "fix-stores-users": "tsx src/scripts/fix-stores-users-collation.ts", "create-store-follows": "tsx src/scripts/create-store-follows-table.ts", "add-store-contact": "tsx src/scripts/add-store-contact-fields.ts", "add-admin-tables": "tsx src/scripts/add-admin-tables.ts", "add-ui-settings": "tsx src/scripts/add-ui-settings.ts", "add-oauth-settings": "tsx src/scripts/add-oauth-settings.ts", "create-wallet-tables": "tsx src/scripts/create-wallet-tables.ts", "add-wallet-tables": "tsx src/scripts/add-wallet-tables.ts", "add-payment-methods-table": "tsx src/scripts/add-payment-methods-table.ts", "fix-user-profile-columns": "tsx src/scripts/fix-user-profile-columns.ts", "update-payment-methods-table": "tsx src/scripts/update-payment-methods-table.ts", "add-fan-page-messages": "tsx src/scripts/add-fan-page-messages.ts", "optimize-db": "tsx src/scripts/optimize-database.ts", "init-settings": "node package-scripts/init-settings.js", "db:generate": "drizzle-kit generate:mysql", "db:push": "drizzle-kit push:mysql", "pm2:start": "node ./node_modules/.bin/pm2 start npm --name \"hifnf\" -- start", "pm2:stop": "node ./node_modules/.bin/pm2 stop hifnf", "pm2:restart": "node ./node_modules/.bin/pm2 restart hifnf", "pm2:status": "node ./node_modules/.bin/pm2 status", "install-pm2": "node install-pm2.js", "start-bg": "node start.js", "stop-bg": "node stop.js"}, "dependencies": {"@auth/core": "0.34.2", "@auth/drizzle-adapter": "^1.9.1", "@aws-sdk/client-s3": "^3.844.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@fullcalendar/core": "^6.1.17", "@fullcalendar/daygrid": "^6.1.17", "@fullcalendar/interaction": "^6.1.17", "@fullcalendar/react": "^6.1.17", "@fullcalendar/timegrid": "^6.1.17", "@headlessui/react": "^2.2.2", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@types/react-beautiful-dnd": "^13.1.8", "bcryptjs": "^3.0.2", "chart.js": "^4.4.9", "cloudinary": "^2.6.1", "clsx": "^2.1.1", "critters": "^0.0.23", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "drizzle-orm": "^0.43.1", "emoji-picker-react": "^4.12.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "joi": "^17.11.0", "lucide-react": "^0.525.0", "mysql2": "^3.14.1", "next": "15.3.2", "next-auth": "^4.24.11", "node-fetch": "^3.3.2", "nodemailer": "^7.0.3", "react": "^19.0.0", "react-beautiful-dnd": "^13.1.1", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.56.3", "react-hot-toast": "^2.5.2", "tailwind-merge": "^3.3.0", "uuid": "^11.1.0", "zod": "^3.24.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcryptjs": "^2.4.6", "@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "drizzle-kit": "^0.31.1", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "tsx": "^4.19.4", "typescript": "^5"}}