/**
 * SSE Diagnostics Utility
 * Helps diagnose and troubleshoot SSE connection issues
 */

export interface SSEDiagnosticResult {
  endpoint: string;
  status: 'success' | 'error' | 'timeout';
  message: string;
  details?: any;
  timestamp: Date;
}

export class SSEDiagnostics {
  private static instance: SSEDiagnostics;
  private diagnosticHistory: SSEDiagnosticResult[] = [];

  static getInstance(): SSEDiagnostics {
    if (!SSEDiagnostics.instance) {
      SSEDiagnostics.instance = new SSEDiagnostics();
    }
    return SSEDiagnostics.instance;
  }

  /**
   * Test SSE endpoint connectivity
   */
  async testEndpoint(endpoint: string, timeout = 10000): Promise<SSEDiagnosticResult> {
    const result: SSEDiagnosticResult = {
      endpoint,
      status: 'error',
      message: '',
      timestamp: new Date()
    };

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      const eventSource = new EventSource(endpoint);
      
      const testPromise = new Promise<void>((resolve, reject) => {
        eventSource.onopen = () => {
          clearTimeout(timeoutId);
          result.status = 'success';
          result.message = 'Connection established successfully';
          eventSource.close();
          resolve();
        };

        eventSource.onerror = (error) => {
          clearTimeout(timeoutId);
          result.status = 'error';
          result.message = 'Connection failed';
          result.details = { error, readyState: eventSource.readyState };
          eventSource.close();
          reject(error);
        };

        controller.signal.addEventListener('abort', () => {
          clearTimeout(timeoutId);
          result.status = 'timeout';
          result.message = `Connection timeout after ${timeout}ms`;
          eventSource.close();
          reject(new Error('Timeout'));
        });
      });

      await testPromise;
    } catch (error) {
      if (result.status !== 'timeout') {
        result.status = 'error';
        result.message = error instanceof Error ? error.message : 'Unknown error';
        result.details = { error };
      }
    }

    this.diagnosticHistory.push(result);
    return result;
  }

  /**
   * Run comprehensive SSE diagnostics
   */
  async runFullDiagnostics(): Promise<SSEDiagnosticResult[]> {
    const endpoints = [
      '/api/sse/messages',
      '/api/sse/notifications',
      '/api/sse/typing'
    ];

    console.log('🔍 Running SSE diagnostics...');
    
    const results = await Promise.all(
      endpoints.map(endpoint => this.testEndpoint(endpoint))
    );

    // Additional browser capability checks
    const browserChecks = this.checkBrowserCapabilities();
    results.push(...browserChecks);

    // Network connectivity check
    const networkCheck = await this.checkNetworkConnectivity();
    results.push(networkCheck);

    console.log('📊 SSE Diagnostics Results:', results);
    return results;
  }

  /**
   * Check browser SSE capabilities
   */
  private checkBrowserCapabilities(): SSEDiagnosticResult[] {
    const results: SSEDiagnosticResult[] = [];

    // EventSource support
    results.push({
      endpoint: 'browser-eventsource',
      status: typeof EventSource !== 'undefined' ? 'success' : 'error',
      message: typeof EventSource !== 'undefined' 
        ? 'EventSource is supported' 
        : 'EventSource is not supported',
      timestamp: new Date()
    });

    // Connection limit check (approximate)
    results.push({
      endpoint: 'browser-connection-limit',
      status: 'success',
      message: `Browser typically allows 6 concurrent connections per domain`,
      details: { 
        userAgent: navigator.userAgent,
        maxConnections: this.estimateMaxConnections()
      },
      timestamp: new Date()
    });

    return results;
  }

  /**
   * Estimate browser connection limits
   */
  private estimateMaxConnections(): number {
    const userAgent = navigator.userAgent.toLowerCase();
    
    if (userAgent.includes('chrome')) return 6;
    if (userAgent.includes('firefox')) return 6;
    if (userAgent.includes('safari')) return 6;
    if (userAgent.includes('edge')) return 6;
    
    return 6; // Default assumption
  }

  /**
   * Check basic network connectivity
   */
  private async checkNetworkConnectivity(): Promise<SSEDiagnosticResult> {
    const result: SSEDiagnosticResult = {
      endpoint: 'network-connectivity',
      status: 'error',
      message: '',
      timestamp: new Date()
    };

    try {
      const response = await fetch('/api/health', { 
        method: 'HEAD',
        cache: 'no-cache'
      });
      
      if (response.ok) {
        result.status = 'success';
        result.message = 'Network connectivity is working';
        result.details = { status: response.status };
      } else {
        result.message = `Network check failed with status: ${response.status}`;
        result.details = { status: response.status };
      }
    } catch (error) {
      result.message = 'Network connectivity check failed';
      result.details = { error: error instanceof Error ? error.message : 'Unknown error' };
    }

    return result;
  }

  /**
   * Get diagnostic history
   */
  getHistory(): SSEDiagnosticResult[] {
    return [...this.diagnosticHistory];
  }

  /**
   * Clear diagnostic history
   */
  clearHistory(): void {
    this.diagnosticHistory = [];
  }

  /**
   * Generate diagnostic report
   */
  generateReport(): string {
    const results = this.getHistory();
    const successCount = results.filter(r => r.status === 'success').length;
    const errorCount = results.filter(r => r.status === 'error').length;
    const timeoutCount = results.filter(r => r.status === 'timeout').length;

    let report = `SSE Diagnostics Report\n`;
    report += `Generated: ${new Date().toISOString()}\n`;
    report += `Total Tests: ${results.length}\n`;
    report += `Success: ${successCount}, Errors: ${errorCount}, Timeouts: ${timeoutCount}\n\n`;

    results.forEach((result, index) => {
      report += `${index + 1}. ${result.endpoint}\n`;
      report += `   Status: ${result.status.toUpperCase()}\n`;
      report += `   Message: ${result.message}\n`;
      report += `   Time: ${result.timestamp.toISOString()}\n`;
      if (result.details) {
        report += `   Details: ${JSON.stringify(result.details, null, 2)}\n`;
      }
      report += `\n`;
    });

    return report;
  }
}

// Export singleton instance
export const sseDiagnostics = SSEDiagnostics.getInstance();

// Utility function for quick diagnostics
export async function runSSEDiagnostics(): Promise<void> {
  const results = await sseDiagnostics.runFullDiagnostics();
  
  console.group('🔍 SSE Diagnostics Summary');
  results.forEach(result => {
    const icon = result.status === 'success' ? '✅' : result.status === 'timeout' ? '⏰' : '❌';
    console.log(`${icon} ${result.endpoint}: ${result.message}`);
  });
  console.groupEnd();
  
  const report = sseDiagnostics.generateReport();
  console.log('📄 Full Report:\n', report);
}
