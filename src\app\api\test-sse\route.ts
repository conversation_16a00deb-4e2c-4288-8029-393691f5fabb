import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { sendMessageToUser } from '@/app/api/sse/messages/route';
import { sendNotificationToUser } from '@/app/api/sse/notifications/route';
import { startTyping, stopTyping } from '@/app/api/sse/typing/route';

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions);
  
  if (!session?.user?.id) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const { type, data } = await request.json();
    const userId = session.user.id;

    switch (type) {
      case 'test_message':
        // Send a test message via SSE
        sendMessageToUser(userId, {
          type: 'new_message',
          message: {
            id: `test-${Date.now()}`,
            content: data.content || 'Test message from <PERSON><PERSON>',
            senderId: 'system',
            receiverId: userId,
            createdAt: new Date().toISOString(),
            sender: {
              id: 'system',
              name: 'System',
              image: null
            }
          },
          timestamp: new Date().toISOString()
        });
        break;

      case 'test_notification':
        // Send a test notification via SSE
        sendNotificationToUser(userId, {
          id: `test-notif-${Date.now()}`,
          type: 'test',
          recipientId: userId,
          senderId: 'system',
          read: false,
          createdAt: new Date().toISOString(),
          sender: {
            id: 'system',
            name: 'System Test',
            image: null
          }
        });
        break;

      case 'test_typing_start':
        // Test typing indicator
        startTyping(data.conversationId || userId, userId);
        break;

      case 'test_typing_stop':
        // Stop typing indicator
        stopTyping(data.conversationId || userId, userId);
        break;

      case 'connection_test':
        // Test all SSE connections
        const testResults = {
          message_sent: false,
          notification_sent: false,
          typing_tested: false
        };

        try {
          sendMessageToUser(userId, {
            type: 'connection_test',
            message: 'SSE connection test successful',
            timestamp: new Date().toISOString()
          });
          testResults.message_sent = true;
        } catch (error) {
          console.error('Message SSE test failed:', error);
        }

        try {
          sendNotificationToUser(userId, {
            id: `connection-test-${Date.now()}`,
            type: 'connection_test',
            recipientId: userId,
            senderId: 'system',
            read: false,
            createdAt: new Date().toISOString()
          });
          testResults.notification_sent = true;
        } catch (error) {
          console.error('Notification SSE test failed:', error);
        }

        try {
          startTyping(userId, userId);
          setTimeout(() => stopTyping(userId, userId), 2000);
          testResults.typing_tested = true;
        } catch (error) {
          console.error('Typing SSE test failed:', error);
        }

        return NextResponse.json({
          success: true,
          message: 'SSE connection test completed',
          results: testResults
        });

      default:
        return NextResponse.json(
          { error: 'Invalid test type' },
          { status: 400 }
        );
    }

    return NextResponse.json({
      success: true,
      message: `SSE ${type} test completed`,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('SSE test error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET endpoint to check SSE status
export async function GET() {
  const session = await getServerSession(authOptions);
  
  if (!session?.user?.id) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  return NextResponse.json({
    success: true,
    message: 'SSE test endpoint is working',
    endpoints: {
      messages: '/api/sse/messages',
      notifications: '/api/sse/notifications',
      typing: '/api/sse/typing'
    },
    timestamp: new Date().toISOString()
  });
}
