import { useCallback, useRef } from 'react';
import { useSession } from 'next-auth/react';

export function useTypingIndicator() {
  const { data: session } = useSession();
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isTypingRef = useRef(false);

  const startTyping = useCallback(async (conversationId: string) => {
    if (!session?.user || isTypingRef.current) return;

    try {
      isTypingRef.current = true;
      
      await fetch('/api/sse/typing', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'start',
          conversationId
        }),
      });

      // Auto-stop typing after 3 seconds
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }

      typingTimeoutRef.current = setTimeout(() => {
        stopTyping(conversationId);
      }, 3000);

    } catch (error) {
      console.error('Error starting typing indicator:', error);
      isTypingRef.current = false;
    }
  }, [session?.user]);

  const stopTyping = useCallback(async (conversationId: string) => {
    if (!session?.user || !isTypingRef.current) return;

    try {
      isTypingRef.current = false;
      
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
        typingTimeoutRef.current = null;
      }

      await fetch('/api/sse/typing', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'stop',
          conversationId
        }),
      });

    } catch (error) {
      console.error('Error stopping typing indicator:', error);
    }
  }, [session?.user]);

  const handleTyping = useCallback((conversationId: string, isTyping: boolean) => {
    if (isTyping) {
      startTyping(conversationId);
    } else {
      stopTyping(conversationId);
    }
  }, [startTyping, stopTyping]);

  return {
    startTyping,
    stopTyping,
    handleTyping
  };
}
