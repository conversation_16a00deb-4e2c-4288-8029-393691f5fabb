"use client";

import { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { toast } from 'react-hot-toast';
import { notificationSound } from '@/lib/utils/notificationSound';
import { useSSENotifications } from '@/hooks/useSSE';

export interface NotificationData {
  id: string;
  type: string;
  read: boolean;
  createdAt: string;
  sender?: {
    id: string;
    name: string;
    image: string;
  };
  post?: {
    id: string;
    content: string;
    userId: string;
  };
  comment?: {
    id: string;
    content: string;
    postId: string;
  };
  message?: {
    id: string;
    content: string;
    senderId: string;
  };
  friendship?: {
    id: string;
    status: string;
  };
  fanPageId?: string;
  fanPagePostId?: string;
  groupId?: string;
  eventId?: string;
  storeId?: string;
  productId?: string;
}

interface UseNotificationsReturn {
  notifications: NotificationData[];
  unreadCount: number;
  isLoading: boolean;
  error: string | null;
  fetchNotifications: () => Promise<void>;
  markAsRead: (notificationId: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  deleteNotification: (notificationId: string) => Promise<void>;
  refreshNotifications: () => void;
}

export function useNotifications(): UseNotificationsReturn {
  const { data: session } = useSession();
  const [notifications, setNotifications] = useState<NotificationData[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // SSE Real-time notifications
  const {
    notifications: sseNotifications,
    unreadCount: sseUnreadCount,
    isConnected,
    setNotifications: setSseNotifications,
    setUnreadCount: setSseUnreadCount
  } = useSSENotifications();

  const fetchNotifications = useCallback(async () => {
    if (!session?.user) return;

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/notifications');

      if (!response.ok) {
        throw new Error('Failed to fetch notifications');
      }

      const data = await response.json();
      setNotifications(data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch notifications';
      setError(errorMessage);
      console.error('Error fetching notifications:', err);
    } finally {
      setIsLoading(false);
    }
  }, [session?.user]);

  const markAsRead = useCallback(async (notificationId: string) => {
    try {
      const response = await fetch(`/api/notifications/${notificationId}`, {
        method: 'PATCH',
      });

      if (!response.ok) {
        throw new Error('Failed to mark notification as read');
      }

      // Update local state
      setNotifications(prev =>
        prev.map(notification =>
          notification.id === notificationId
            ? { ...notification, read: true }
            : notification
        )
      );
    } catch (err) {
      console.error('Error marking notification as read:', err);
      toast.error('Failed to mark notification as read');
    }
  }, []);

  const markAllAsRead = useCallback(async () => {
    try {
      const response = await fetch('/api/notifications', {
        method: 'PATCH',
      });

      if (!response.ok) {
        throw new Error('Failed to mark all notifications as read');
      }

      // Update local state
      setNotifications(prev =>
        prev.map(notification => ({ ...notification, read: true }))
      );

      toast.success('All notifications marked as read');
    } catch (err) {
      console.error('Error marking all notifications as read:', err);
      toast.error('Failed to mark all notifications as read');
    }
  }, []);

  const deleteNotification = useCallback(async (notificationId: string) => {
    try {
      const response = await fetch(`/api/notifications/${notificationId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete notification');
      }

      // Update local state
      setNotifications(prev =>
        prev.filter(notification => notification.id !== notificationId)
      );

      toast.success('Notification deleted');
    } catch (err) {
      console.error('Error deleting notification:', err);
      toast.error('Failed to delete notification');
    }
  }, []);

  const refreshNotifications = useCallback(() => {
    fetchNotifications();
  }, [fetchNotifications]);

  // Helper function to get notification text
  const getNotificationText = (notification: NotificationData) => {
    const senderName = notification.sender?.name || 'Someone';

    switch (notification.type) {
      case 'like':
        return `${senderName} liked your post`;
      case 'comment':
        return `${senderName} commented on your post`;
      case 'friend_request':
        return `${senderName} sent you a friend request`;
      case 'friend_accept':
        return `${senderName} accepted your friend request`;
      case 'message':
        return `${senderName} sent you a message`;
      default:
        return `${senderName} interacted with your content`;
    }
  };

  // Calculate unread count (prefer SSE count if connected, otherwise local count)
  const unreadCount = isConnected && sseUnreadCount >= 0
    ? sseUnreadCount
    : notifications.filter(notification => !notification.read).length;

  // Fetch notifications on mount and when session changes
  useEffect(() => {
    if (session?.user) {
      fetchNotifications();
    }
  }, [session?.user, fetchNotifications]);

  // Handle real-time notifications from SSE
  useEffect(() => {
    if (sseNotifications.length > 0) {
      const latestNotification = sseNotifications[0]; // SSE notifications are added to the beginning

      // Check if notification already exists to avoid duplicates
      const exists = notifications.some(notif => notif.id === latestNotification.id);
      if (!exists) {
        setNotifications(prev => [latestNotification, ...prev]);

        // Play notification sound
        try {
          notificationSound.play();
        } catch (error) {
          console.error('Error playing notification sound:', error);
        }

        // Show toast notification
        toast.success(`New ${latestNotification.type} notification`, {
          duration: 3000,
        });
      }
    }
  }, [sseNotifications, notifications]);

  // Sync SSE notifications with local state on initial load
  useEffect(() => {
    if (notifications.length > 0 && sseNotifications.length === 0) {
      setSseNotifications(notifications);
      setSseUnreadCount(notifications.filter(n => !n.read).length);
    }
  }, [notifications, sseNotifications.length, setSseNotifications, setSseUnreadCount]);

  // Auto-refresh notifications every 60 seconds (reduced frequency due to SSE)
  useEffect(() => {
    if (!session?.user || !isConnected) return;

    const interval = setInterval(() => {
      fetchNotifications();
    }, 60000); // 60 seconds (reduced from 30)

    return () => clearInterval(interval);
  }, [session?.user, isConnected, fetchNotifications]);

  return {
    notifications,
    unreadCount,
    isLoading,
    error,
    fetchNotifications,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    refreshNotifications,
  };
}
