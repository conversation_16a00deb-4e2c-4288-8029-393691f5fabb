import { NextRequest } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

// Store active notification SSE connections
const notificationConnections = new Map<string, WritableStreamDefaultWriter>();
const userNotificationConnections = new Map<string, Set<string>>();

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);
  
  if (!session?.user?.id) {
    return new Response('Unauthorized', { status: 401 });
  }

  const userId = session.user.id;
  const connectionId = `notification-${userId}-${Date.now()}`;

  // Create SSE stream for notifications
  const stream = new ReadableStream({
    start(controller) {
      const encoder = new TextEncoder();
      
      // Send initial connection message
      const initialMessage = `data: ${JSON.stringify({
        type: 'notification_connected',
        userId,
        timestamp: new Date().toISOString()
      })}\n\n`;
      
      controller.enqueue(encoder.encode(initialMessage));

      // Store connection
      const writer = {
        write: (data: string) => {
          try {
            controller.enqueue(encoder.encode(data));
          } catch (error) {
            console.error('Notification SSE write error:', error);
          }
        },
        close: () => {
          try {
            controller.close();
          } catch (error) {
            console.error('Notification SSE close error:', error);
          }
        }
      };

      notificationConnections.set(connectionId, writer as any);
      
      // Track user notification connections
      if (!userNotificationConnections.has(userId)) {
        userNotificationConnections.set(userId, new Set());
      }
      userNotificationConnections.get(userId)!.add(connectionId);

      // Send heartbeat every 25 seconds (less than typical 30s timeout)
      const heartbeat = setInterval(() => {
        try {
          const heartbeatMessage = `data: ${JSON.stringify({
            type: 'notification_heartbeat',
            timestamp: new Date().toISOString()
          })}\n\n`;

          writer.write(heartbeatMessage);
        } catch (error) {
          console.error('Notification heartbeat failed for connection:', connectionId, error);
          clearInterval(heartbeat);
          cleanup();
        }
      }, 25000);

      // Cleanup function
      const cleanup = () => {
        console.log(`🧹 Cleaning up notification SSE connection: ${connectionId}`);
        clearInterval(heartbeat);
        notificationConnections.delete(connectionId);

        const userConns = userNotificationConnections.get(userId);
        if (userConns) {
          userConns.delete(connectionId);
          if (userConns.size === 0) {
            userNotificationConnections.delete(userId);
          }
        }

        try {
          controller.close();
        } catch (error) {
          // Connection might already be closed
        }
      };

      // Handle client disconnect
      request.signal.addEventListener('abort', cleanup);

      // Set up connection timeout (5 minutes)
      const connectionTimeout = setTimeout(() => {
        console.log(`⏰ Notification connection timeout for: ${connectionId}`);
        cleanup();
      }, 5 * 60 * 1000);
    }
  });

  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control',
      'X-Accel-Buffering': 'no', // Disable nginx buffering
    },
  });
}

// Function to send notification to specific user
export function sendNotificationToUser(userId: string, notification: any) {
  const userConns = userNotificationConnections.get(userId);
  if (!userConns) return;

  const message = `data: ${JSON.stringify({
    type: 'new_notification',
    notification,
    timestamp: new Date().toISOString()
  })}\n\n`;
  
  userConns.forEach(connectionId => {
    const writer = notificationConnections.get(connectionId);
    if (writer) {
      try {
        writer.write(message);
      } catch (error) {
        console.error('Error sending notification to user:', error);
        // Remove failed connection
        notificationConnections.delete(connectionId);
        userConns.delete(connectionId);
      }
    }
  });
}

// Function to send notification update (mark as read, etc.)
export function sendNotificationUpdate(userId: string, update: any) {
  const userConns = userNotificationConnections.get(userId);
  if (!userConns) return;

  const message = `data: ${JSON.stringify({
    type: 'notification_update',
    update,
    timestamp: new Date().toISOString()
  })}\n\n`;
  
  userConns.forEach(connectionId => {
    const writer = notificationConnections.get(connectionId);
    if (writer) {
      try {
        writer.write(message);
      } catch (error) {
        console.error('Error sending notification update:', error);
        notificationConnections.delete(connectionId);
        userConns.delete(connectionId);
      }
    }
  });
}
