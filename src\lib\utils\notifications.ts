interface NotificationData {
  id: string;
  type: string;
  recipientId: string;
  senderId: string;
  read: boolean;
  createdAt: string;
  sender?: {
    id: string;
    name: string;
    image: string;
  };
  postId?: string;
  commentId?: string;
  messageId?: string;
  friendshipId?: string;
  fanPageId?: string;
  fanPagePostId?: string;
  groupId?: string;
  eventId?: string;
  storeId?: string;
  productId?: string;
}

// Real-time notification implementation with SSE
export function emitNotification(notification: NotificationData) {
  try {
    // Import SSE function dynamically to avoid circular imports
    import('@/app/api/sse/notifications/route').then(({ sendNotificationToUser }) => {
      sendNotificationToUser(notification.recipientId, notification);
      console.log('📡 Notification sent via SSE:', notification.type);
    }).catch(error => {
      console.error('Error sending notification via SSE:', error);
    });
  } catch (error) {
    console.error('Error emitting notification:', error);
  }
}

// Helper function to create notification data
export function createNotificationData(
  type: string,
  recipientId: string,
  senderId: string,
  options: {
    postId?: string;
    commentId?: string;
    messageId?: string;
    friendshipId?: string;
    fanPageId?: string;
    fanPagePostId?: string;
    groupId?: string;
    eventId?: string;
    storeId?: string;
    productId?: string;
    sender?: {
      id: string;
      name: string;
      image: string;
    };
  } = {}
): NotificationData {
  return {
    id: '', // Will be set by the database
    type,
    recipientId,
    senderId,
    read: false,
    createdAt: new Date().toISOString(),
    ...options,
  };
}
