import { NextRequest } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

// Store active SSE connections
const connections = new Map<string, WritableStreamDefaultWriter>();
const userConnections = new Map<string, Set<string>>();

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);
  
  if (!session?.user?.id) {
    return new Response('Unauthorized', { status: 401 });
  }

  const userId = session.user.id;
  const connectionId = `${userId}-${Date.now()}`;

  // Create SSE stream
  const stream = new ReadableStream({
    start(controller) {
      const encoder = new TextEncoder();
      
      // Send initial connection message
      const initialMessage = `data: ${JSON.stringify({
        type: 'connected',
        userId,
        timestamp: new Date().toISOString()
      })}\n\n`;
      
      controller.enqueue(encoder.encode(initialMessage));

      // Store connection
      const writer = {
        write: (data: string) => {
          try {
            controller.enqueue(encoder.encode(data));
          } catch (error) {
            console.error('SSE write error:', error);
          }
        },
        close: () => {
          try {
            controller.close();
          } catch (error) {
            console.error('SSE close error:', error);
          }
        }
      };

      connections.set(connectionId, writer as any);
      
      // Track user connections
      if (!userConnections.has(userId)) {
        userConnections.set(userId, new Set());
      }
      userConnections.get(userId)!.add(connectionId);

      // Send heartbeat every 25 seconds (less than typical 30s timeout)
      const heartbeat = setInterval(() => {
        try {
          const heartbeatMessage = `data: ${JSON.stringify({
            type: 'heartbeat',
            timestamp: new Date().toISOString()
          })}\n\n`;

          writer.write(heartbeatMessage);
        } catch (error) {
          console.error('Heartbeat failed for connection:', connectionId, error);
          clearInterval(heartbeat);
          cleanup();
        }
      }, 25000);

      // Cleanup function
      const cleanup = () => {
        console.log(`🧹 Cleaning up SSE connection: ${connectionId}`);
        clearInterval(heartbeat);
        connections.delete(connectionId);

        const userConns = userConnections.get(userId);
        if (userConns) {
          userConns.delete(connectionId);
          if (userConns.size === 0) {
            userConnections.delete(userId);
          }
        }

        try {
          controller.close();
        } catch (error) {
          // Connection might already be closed
        }
      };

      // Handle client disconnect
      request.signal.addEventListener('abort', cleanup);

      // Set up connection timeout (5 minutes)
      const connectionTimeout = setTimeout(() => {
        console.log(`⏰ Connection timeout for: ${connectionId}`);
        cleanup();
      }, 5 * 60 * 1000);
    }
  });

  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control',
      'X-Accel-Buffering': 'no', // Disable nginx buffering
    },
  });
}

// Function to send message to specific user
export function sendMessageToUser(userId: string, data: any) {
  const userConns = userConnections.get(userId);
  if (!userConns) return;

  const message = `data: ${JSON.stringify(data)}\n\n`;
  
  userConns.forEach(connectionId => {
    const writer = connections.get(connectionId);
    if (writer) {
      try {
        writer.write(message);
      } catch (error) {
        console.error('Error sending message to user:', error);
        // Remove failed connection
        connections.delete(connectionId);
        userConns.delete(connectionId);
      }
    }
  });
}

// Function to broadcast to all connected users
export function broadcastMessage(data: any) {
  const message = `data: ${JSON.stringify(data)}\n\n`;
  
  connections.forEach((writer, connectionId) => {
    try {
      writer.write(message);
    } catch (error) {
      console.error('Error broadcasting message:', error);
      connections.delete(connectionId);
    }
  });
}
