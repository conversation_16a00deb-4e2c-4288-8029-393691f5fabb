"use client";

import { useState, useEffect } from 'react';
import { useSSEMessages, useSSENotifications } from '@/hooks/useSSE';

interface ConnectionStatus {
  endpoint: string;
  isConnected: boolean;
  connectionState: string;
  lastActivity: Date | null;
}

export function SSEConnectionMonitor() {
  const [isVisible, setIsVisible] = useState(false);
  const [connectionHistory, setConnectionHistory] = useState<string[]>([]);
  
  const messageSSE = useSSEMessages();
  const notificationSSE = useSSENotifications();

  // Only show in development
  useEffect(() => {
    setIsVisible(process.env.NODE_ENV === 'development');
  }, []);

  // Track connection changes
  useEffect(() => {
    const timestamp = new Date().toLocaleTimeString();
    const status = `[${timestamp}] Messages: ${messageSSE.connectionState} | Notifications: ${notificationSSE.connectionState}`;
    
    setConnectionHistory(prev => {
      const newHistory = [status, ...prev.slice(0, 9)]; // Keep last 10 entries
      return newHistory;
    });
  }, [messageSSE.connectionState, notificationSSE.connectionState]);

  if (!isVisible) {
    return null;
  }

  const connections: ConnectionStatus[] = [
    {
      endpoint: '/api/sse/messages',
      isConnected: messageSSE.isConnected,
      connectionState: messageSSE.connectionState,
      lastActivity: new Date()
    },
    {
      endpoint: '/api/sse/notifications',
      isConnected: notificationSSE.isConnected,
      connectionState: notificationSSE.connectionState,
      lastActivity: new Date()
    }
  ];

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg shadow-lg p-4 max-w-md z-50">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-semibold text-gray-800">SSE Monitor</h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-500 hover:text-gray-700 text-xs"
        >
          ✕
        </button>
      </div>
      
      <div className="space-y-2">
        {connections.map((conn) => (
          <div key={conn.endpoint} className="flex items-center justify-between text-xs">
            <span className="text-gray-600 truncate">{conn.endpoint}</span>
            <div className="flex items-center space-x-2">
              <span className={`px-2 py-1 rounded text-xs font-medium ${
                conn.isConnected 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-red-100 text-red-800'
              }`}>
                {conn.connectionState}
              </span>
              <div className={`w-2 h-2 rounded-full ${
                conn.isConnected ? 'bg-green-500' : 'bg-red-500'
              }`} />
            </div>
          </div>
        ))}
      </div>

      <div className="mt-3 pt-3 border-t border-gray-200">
        <div className="flex items-center justify-between mb-2">
          <span className="text-xs font-medium text-gray-700">Connection History</span>
          <button
            onClick={() => setConnectionHistory([])}
            className="text-xs text-gray-500 hover:text-gray-700"
          >
            Clear
          </button>
        </div>
        <div className="max-h-32 overflow-y-auto space-y-1">
          {connectionHistory.map((entry, index) => (
            <div key={index} className="text-xs text-gray-600 font-mono">
              {entry}
            </div>
          ))}
        </div>
      </div>

      <div className="mt-3 pt-3 border-t border-gray-200 flex space-x-2">
        <button
          onClick={() => {
            messageSSE.reconnect();
            notificationSSE.reconnect();
          }}
          className="text-xs bg-blue-500 text-white px-2 py-1 rounded hover:bg-blue-600"
        >
          Reconnect All
        </button>
        <button
          onClick={() => {
            messageSSE.disconnect();
            notificationSSE.disconnect();
          }}
          className="text-xs bg-gray-500 text-white px-2 py-1 rounded hover:bg-gray-600"
        >
          Disconnect All
        </button>
      </div>
    </div>
  );
}

// Hook to show/hide the monitor
export function useSSEMonitor() {
  const [showMonitor, setShowMonitor] = useState(false);

  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      // Ctrl+Shift+S to toggle monitor
      if (event.ctrlKey && event.shiftKey && event.key === 'S') {
        event.preventDefault();
        setShowMonitor(prev => !prev);
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, []);

  return { showMonitor, setShowMonitor };
}
