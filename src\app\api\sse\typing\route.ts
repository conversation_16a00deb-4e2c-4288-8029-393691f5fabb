import { NextRequest } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

// Store typing indicators
const typingUsers = new Map<string, Set<string>>(); // conversationId -> Set of userIds
const typingConnections = new Map<string, WritableStreamDefaultWriter>();
const userTypingConnections = new Map<string, Set<string>>();

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);
  
  if (!session?.user?.id) {
    return new Response('Unauthorized', { status: 401 });
  }

  const userId = session.user.id;
  const connectionId = `typing-${userId}-${Date.now()}`;

  // Create SSE stream for typing indicators
  const stream = new ReadableStream({
    start(controller) {
      const encoder = new TextEncoder();
      
      // Send initial connection message
      const initialMessage = `data: ${JSON.stringify({
        type: 'typing_connected',
        userId,
        timestamp: new Date().toISOString()
      })}\n\n`;
      
      controller.enqueue(encoder.encode(initialMessage));

      // Store connection
      const writer = {
        write: (data: string) => {
          try {
            controller.enqueue(encoder.encode(data));
          } catch (error) {
            console.error('Typing SSE write error:', error);
          }
        },
        close: () => {
          try {
            controller.close();
          } catch (error) {
            console.error('Typing SSE close error:', error);
          }
        }
      };

      typingConnections.set(connectionId, writer as any);
      
      // Track user typing connections
      if (!userTypingConnections.has(userId)) {
        userTypingConnections.set(userId, new Set());
      }
      userTypingConnections.get(userId)!.add(connectionId);

      // Cleanup function
      const cleanup = () => {
        typingConnections.delete(connectionId);
        
        const userConns = userTypingConnections.get(userId);
        if (userConns) {
          userConns.delete(connectionId);
          if (userConns.size === 0) {
            userTypingConnections.delete(userId);
          }
        }

        // Remove user from all typing indicators
        typingUsers.forEach((users, conversationId) => {
          if (users.has(userId)) {
            users.delete(userId);
            broadcastTypingUpdate(conversationId);
          }
        });
      };

      // Handle client disconnect
      request.signal.addEventListener('abort', cleanup);
    }
  });

  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control',
    },
  });
}

// Function to handle typing start
export function startTyping(conversationId: string, userId: string) {
  if (!typingUsers.has(conversationId)) {
    typingUsers.set(conversationId, new Set());
  }
  
  typingUsers.get(conversationId)!.add(userId);
  broadcastTypingUpdate(conversationId);

  // Auto-stop typing after 3 seconds
  setTimeout(() => {
    stopTyping(conversationId, userId);
  }, 3000);
}

// Function to handle typing stop
export function stopTyping(conversationId: string, userId: string) {
  const users = typingUsers.get(conversationId);
  if (users && users.has(userId)) {
    users.delete(userId);
    broadcastTypingUpdate(conversationId);
  }
}

// Function to broadcast typing update to conversation participants
function broadcastTypingUpdate(conversationId: string) {
  const typingUsersList = Array.from(typingUsers.get(conversationId) || []);
  
  const message = `data: ${JSON.stringify({
    type: 'typing_update',
    conversationId,
    typingUsers: typingUsersList,
    timestamp: new Date().toISOString()
  })}\n\n`;

  // Send to all connected users
  typingConnections.forEach((writer, connectionId) => {
    try {
      writer.write(message);
    } catch (error) {
      console.error('Error broadcasting typing update:', error);
      typingConnections.delete(connectionId);
    }
  });
}

// API endpoint to handle typing events
export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions);
  
  if (!session?.user?.id) {
    return new Response('Unauthorized', { status: 401 });
  }

  try {
    const { action, conversationId } = await request.json();
    const userId = session.user.id;

    if (action === 'start') {
      startTyping(conversationId, userId);
    } else if (action === 'stop') {
      stopTyping(conversationId, userId);
    }

    return new Response(JSON.stringify({ success: true }), {
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Typing API error:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
}
