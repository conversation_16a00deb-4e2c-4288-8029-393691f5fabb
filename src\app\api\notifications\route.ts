import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { notifications, users, posts, comments, messages, fanPages, fanPagePosts, subscriptions } from "@/lib/db/schema";
import { eq, desc } from "drizzle-orm";
import { sendNotificationUpdate } from "@/app/api/sse/notifications/route";

export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Fetch user's notifications from the database
    const userNotifications = await db.query.notifications.findMany({
      where: eq(notifications.recipientId, session.user.id),
      orderBy: [desc(notifications.createdAt)],
      with: {
        sender: {
          columns: {
            id: true,
            name: true,
            image: true,
          },
        },
        post: {
          columns: {
            id: true,
            content: true,
            userId: true,
          },
        },
        comment: {
          columns: {
            id: true,
            content: true,
            postId: true,
          },
        },
        message: {
          columns: {
            id: true,
            content: true,
            senderId: true,
          },
        },
        subscription: {
          columns: {
            id: true,
            subscriberId: true,
            targetUserId: true,
          },
        },
      },
      limit: 50, // Limit to last 50 notifications
    });

    // Format notifications for frontend
    const formattedNotifications = userNotifications.map(notification => ({
      id: notification.id,
      type: notification.type,
      read: notification.read,
      createdAt: notification.createdAt,
      sender: notification.sender,
      post: notification.post,
      comment: notification.comment,
      message: notification.message,
      subscription: notification.subscription,
      fanPageId: notification.fanPageId,
      fanPagePostId: notification.fanPagePostId,
      groupId: notification.groupId,
      eventId: notification.eventId,
      storeId: notification.storeId,
      productId: notification.productId,
    }));

    return NextResponse.json(formattedNotifications);
  } catch (error) {
    console.error("Error fetching notifications:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function PATCH() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Mark all notifications as read
    await db
      .update(notifications)
      .set({ read: true })
      .where(eq(notifications.recipientId, session.user.id));

    // Send real-time update via SSE
    try {
      sendNotificationUpdate(session.user.id, {
        type: 'mark_all_read',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error sending notification update via SSE:', error);
    }

    return NextResponse.json(
      { message: "All notifications marked as read" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error marking notifications as read:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
