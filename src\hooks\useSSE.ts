import { useEffect, useRef, useState, useCallback } from 'react';
import { useSession } from 'next-auth/react';

interface SSEMessage {
  type: string;
  [key: string]: any;
}

interface UseSSEOptions {
  endpoint: string;
  onMessage?: (message: SSEMessage) => void;
  onError?: (error: Event) => void;
  onConnect?: () => void;
  onDisconnect?: () => void;
  autoReconnect?: boolean;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
}

// Global connection manager to prevent multiple connections to the same endpoint
class SSEConnectionManager {
  private connections = new Map<string, {
    eventSource: EventSource;
    subscribers: Set<string>;
    callbacks: Map<string, {
      onMessage?: (message: SSEMessage) => void;
      onError?: (error: Event) => void;
      onConnect?: () => void;
      onDisconnect?: () => void;
    }>;
  }>();

  subscribe(
    endpoint: string,
    subscriberId: string,
    callbacks: {
      onMessage?: (message: SSEMessage) => void;
      onError?: (error: Event) => void;
      onConnect?: () => void;
      onDisconnect?: () => void;
    }
  ): EventSource | null {
    let connection = this.connections.get(endpoint);

    if (!connection) {
      try {
        const eventSource = new EventSource(endpoint);
        connection = {
          eventSource,
          subscribers: new Set(),
          callbacks: new Map()
        };

        eventSource.onopen = () => {
          connection!.callbacks.forEach(cb => cb.onConnect?.());
        };

        eventSource.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            connection!.callbacks.forEach(cb => cb.onMessage?.(data));
          } catch (error) {
            console.error('Error parsing SSE message:', error);
          }
        };

        eventSource.onerror = (error) => {
          connection!.callbacks.forEach(cb => {
            cb.onError?.(error);
            cb.onDisconnect?.();
          });
        };

        this.connections.set(endpoint, connection);
      } catch (error) {
        console.error('Error creating EventSource:', error);
        return null;
      }
    }

    connection.subscribers.add(subscriberId);
    connection.callbacks.set(subscriberId, callbacks);

    return connection.eventSource;
  }

  unsubscribe(endpoint: string, subscriberId: string) {
    const connection = this.connections.get(endpoint);
    if (!connection) return;

    connection.subscribers.delete(subscriberId);
    connection.callbacks.delete(subscriberId);

    if (connection.subscribers.size === 0) {
      connection.eventSource.close();
      this.connections.delete(endpoint);
    }
  }

  getConnectionState(endpoint: string): number | null {
    const connection = this.connections.get(endpoint);
    return connection?.eventSource.readyState ?? null;
  }
}

const sseManager = new SSEConnectionManager();

export function useSSE({
  endpoint,
  onMessage,
  onError,
  onConnect,
  onDisconnect,
  autoReconnect = true,
  reconnectInterval = 5000,
  maxReconnectAttempts = 3
}: UseSSEOptions) {
  const { data: session, status } = useSession();
  const [isConnected, setIsConnected] = useState(false);
  const [connectionState, setConnectionState] = useState<'disconnected' | 'connecting' | 'connected'>('disconnected');
  const subscriberIdRef = useRef<string>(`${endpoint}-${Math.random().toString(36).substr(2, 9)}`);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttemptsRef = useRef(0);
  const isUnmountedRef = useRef(false);

  const connect = useCallback(() => {
    if (!session?.user || status !== 'authenticated' || isUnmountedRef.current) {
      return;
    }

    // Clear any existing reconnect timeout
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    setConnectionState('connecting');

    const eventSource = sseManager.subscribe(endpoint, subscriberIdRef.current, {
      onMessage,
      onError: (error) => {
        if (isUnmountedRef.current) return;

        setIsConnected(false);
        setConnectionState('disconnected');
        onError?.(error);
        onDisconnect?.();

        // Check if we should attempt reconnection
        const readyState = sseManager.getConnectionState(endpoint);
        if (autoReconnect &&
            reconnectAttemptsRef.current < maxReconnectAttempts &&
            readyState !== EventSource.OPEN &&
            !isUnmountedRef.current) {

          reconnectAttemptsRef.current++;
          const delay = Math.min(reconnectInterval * reconnectAttemptsRef.current, 30000); // Cap at 30 seconds

          console.log(`🔄 SSE reconnecting to ${endpoint} (${reconnectAttemptsRef.current}/${maxReconnectAttempts}) in ${delay}ms`);

          reconnectTimeoutRef.current = setTimeout(() => {
            if (!isUnmountedRef.current) {
              connect();
            }
          }, delay);
        } else if (reconnectAttemptsRef.current >= maxReconnectAttempts) {
          console.error(`❌ SSE max reconnection attempts reached for ${endpoint}`);
        }
      },
      onConnect: () => {
        if (isUnmountedRef.current) return;

        setIsConnected(true);
        setConnectionState('connected');
        reconnectAttemptsRef.current = 0;
        onConnect?.();
      },
      onDisconnect: () => {
        if (isUnmountedRef.current) return;

        setIsConnected(false);
        setConnectionState('disconnected');
        onDisconnect?.();
      }
    });

    if (!eventSource) {
      setConnectionState('disconnected');
      return;
    }

    // Check initial connection state
    if (eventSource.readyState === EventSource.OPEN) {
      setIsConnected(true);
      setConnectionState('connected');
      reconnectAttemptsRef.current = 0;
      onConnect?.();
    }
  }, [session?.user, status, endpoint, onMessage, onError, onConnect, onDisconnect, autoReconnect, reconnectInterval, maxReconnectAttempts]);

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    sseManager.unsubscribe(endpoint, subscriberIdRef.current);
    setIsConnected(false);
    setConnectionState('disconnected');
  }, [endpoint]);

  const reconnect = useCallback(() => {
    disconnect();
    reconnectAttemptsRef.current = 0;
    setTimeout(() => {
      if (!isUnmountedRef.current) {
        connect();
      }
    }, 1000);
  }, [connect, disconnect]);

  // Connect when session is available
  useEffect(() => {
    isUnmountedRef.current = false;

    if (session?.user && status === 'authenticated') {
      connect();
    } else {
      disconnect();
    }

    return () => {
      disconnect();
    };
  }, [session?.user, status, connect, disconnect]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isUnmountedRef.current = true;
      disconnect();
    };
  }, [disconnect]);

  return {
    isConnected,
    connectionState,
    connect,
    disconnect,
    reconnect
  };
}

// Specialized hook for messages
export function useSSEMessages() {
  const [messages, setMessages] = useState<any[]>([]);
  const [typingUsers, setTypingUsers] = useState<string[]>([]);

  const handleMessage = useCallback((message: SSEMessage) => {
    switch (message.type) {
      case 'new_message':
        setMessages(prev => {
          // Prevent duplicate messages
          const exists = prev.some(msg => msg.id === message.message?.id);
          if (exists) return prev;
          return [...prev, message.message];
        });
        break;
      case 'message_update':
        setMessages(prev =>
          prev.map(msg =>
            msg.id === message.messageId
              ? { ...msg, ...message.updates }
              : msg
          )
        );
        break;
      case 'typing_update':
        setTypingUsers(message.typingUsers || []);
        break;
      case 'connected':
        console.log('📡 Connected to message stream');
        break;
      case 'heartbeat':
        // Keep connection alive - no action needed
        break;
      default:
        // Log unknown message types for debugging
        console.debug('Unknown SSE message type:', message.type);
    }
  }, []);

  const handleError = useCallback((error: Event) => {
    console.error('Message SSE error:', error);
  }, []);

  const sse = useSSE({
    endpoint: '/api/sse/messages',
    onMessage: handleMessage,
    onError: handleError,
    onConnect: () => console.log('🔗 Message SSE connected'),
    onDisconnect: () => console.log('❌ Message SSE disconnected'),
    reconnectInterval: 5000,
    maxReconnectAttempts: 3
  });

  return {
    ...sse,
    messages,
    typingUsers,
    setMessages
  };
}

// Specialized hook for notifications
export function useSSENotifications() {
  const [notifications, setNotifications] = useState<any[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);

  const handleNotification = useCallback((message: SSEMessage) => {
    switch (message.type) {
      case 'new_notification':
        setNotifications(prev => {
          // Prevent duplicate notifications
          const exists = prev.some(notif => notif.id === message.notification?.id);
          if (exists) return prev;
          return [message.notification, ...prev];
        });
        setUnreadCount(prev => prev + 1);
        break;
      case 'notification_update':
        if (message.update.read) {
          setUnreadCount(prev => Math.max(0, prev - 1));
        }
        setNotifications(prev =>
          prev.map(notif =>
            notif.id === message.update.notificationId
              ? { ...notif, ...message.update }
              : notif
          )
        );
        break;
      case 'notification_connected':
        console.log('📡 Connected to notification stream');
        break;
      case 'notification_heartbeat':
        // Keep connection alive - no action needed
        break;
      default:
        // Log unknown message types for debugging
        console.debug('Unknown notification SSE message type:', message.type);
    }
  }, []);

  const handleError = useCallback((error: Event) => {
    console.error('Notification SSE error:', error);
  }, []);

  const sse = useSSE({
    endpoint: '/api/sse/notifications',
    onMessage: handleNotification,
    onError: handleError,
    onConnect: () => console.log('🔔 Notification SSE connected'),
    onDisconnect: () => console.log('❌ Notification SSE disconnected'),
    reconnectInterval: 5000,
    maxReconnectAttempts: 3
  });

  return {
    ...sse,
    notifications,
    unreadCount,
    setNotifications,
    setUnreadCount
  };
}
