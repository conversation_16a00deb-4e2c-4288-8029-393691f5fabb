import { useEffect, useRef, useState, useCallback } from 'react';
import { useSession } from 'next-auth/react';

interface SSEMessage {
  type: string;
  [key: string]: any;
}

interface UseSSEOptions {
  endpoint: string;
  onMessage?: (message: SSEMessage) => void;
  onError?: (error: Event) => void;
  onConnect?: () => void;
  onDisconnect?: () => void;
  autoReconnect?: boolean;
  reconnectInterval?: number;
}

export function useSSE({
  endpoint,
  onMessage,
  onError,
  onConnect,
  onDisconnect,
  autoReconnect = true,
  reconnectInterval = 3000
}: UseSSEOptions) {
  const { data: session, status } = useSession();
  const [isConnected, setIsConnected] = useState(false);
  const [connectionState, setConnectionState] = useState<'disconnected' | 'connecting' | 'connected'>('disconnected');
  const eventSourceRef = useRef<EventSource | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttemptsRef = useRef(0);
  const maxReconnectAttempts = 5;

  const connect = useCallback(() => {
    if (!session?.user || status !== 'authenticated') {
      return;
    }

    if (eventSourceRef.current) {
      eventSourceRef.current.close();
    }

    setConnectionState('connecting');

    try {
      const eventSource = new EventSource(endpoint);
      eventSourceRef.current = eventSource;

      eventSource.onopen = () => {
        setIsConnected(true);
        setConnectionState('connected');
        reconnectAttemptsRef.current = 0;
        onConnect?.();
      };

      eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          onMessage?.(data);
        } catch (error) {
          console.error('Error parsing SSE message:', error);
        }
      };

      eventSource.onerror = (error) => {
        setIsConnected(false);
        setConnectionState('disconnected');
        onError?.(error);
        onDisconnect?.();

        // Auto-reconnect logic
        if (autoReconnect && reconnectAttemptsRef.current < maxReconnectAttempts) {
          reconnectAttemptsRef.current++;
          const delay = reconnectInterval * Math.pow(2, reconnectAttemptsRef.current - 1); // Exponential backoff
          
          reconnectTimeoutRef.current = setTimeout(() => {
            console.log(`Attempting to reconnect (${reconnectAttemptsRef.current}/${maxReconnectAttempts})...`);
            connect();
          }, delay);
        }
      };

    } catch (error) {
      console.error('Error creating EventSource:', error);
      setConnectionState('disconnected');
    }
  }, [session?.user, status, endpoint, onMessage, onError, onConnect, onDisconnect, autoReconnect, reconnectInterval]);

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }

    setIsConnected(false);
    setConnectionState('disconnected');
    onDisconnect?.();
  }, [onDisconnect]);

  const reconnect = useCallback(() => {
    disconnect();
    reconnectAttemptsRef.current = 0;
    setTimeout(connect, 1000);
  }, [connect, disconnect]);

  // Connect when session is available
  useEffect(() => {
    if (session?.user && status === 'authenticated') {
      connect();
    } else {
      disconnect();
    }

    return () => {
      disconnect();
    };
  }, [session?.user, status, connect, disconnect]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  return {
    isConnected,
    connectionState,
    connect,
    disconnect,
    reconnect
  };
}

// Specialized hook for messages
export function useSSEMessages() {
  const [messages, setMessages] = useState<any[]>([]);
  const [typingUsers, setTypingUsers] = useState<string[]>([]);

  const handleMessage = useCallback((message: SSEMessage) => {
    switch (message.type) {
      case 'new_message':
        setMessages(prev => [...prev, message.message]);
        break;
      case 'message_update':
        setMessages(prev => 
          prev.map(msg => 
            msg.id === message.messageId 
              ? { ...msg, ...message.updates }
              : msg
          )
        );
        break;
      case 'typing_update':
        setTypingUsers(message.typingUsers || []);
        break;
      case 'connected':
        console.log('📡 Connected to message stream');
        break;
      case 'heartbeat':
        // Keep connection alive
        break;
    }
  }, []);

  const sse = useSSE({
    endpoint: '/api/sse/messages',
    onMessage: handleMessage,
    onConnect: () => console.log('🔗 Message SSE connected'),
    onDisconnect: () => console.log('❌ Message SSE disconnected'),
  });

  return {
    ...sse,
    messages,
    typingUsers,
    setMessages
  };
}

// Specialized hook for notifications
export function useSSENotifications() {
  const [notifications, setNotifications] = useState<any[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);

  const handleNotification = useCallback((message: SSEMessage) => {
    switch (message.type) {
      case 'new_notification':
        setNotifications(prev => [message.notification, ...prev]);
        setUnreadCount(prev => prev + 1);
        break;
      case 'notification_update':
        if (message.update.read) {
          setUnreadCount(prev => Math.max(0, prev - 1));
        }
        setNotifications(prev =>
          prev.map(notif =>
            notif.id === message.update.notificationId
              ? { ...notif, ...message.update }
              : notif
          )
        );
        break;
      case 'notification_connected':
        console.log('📡 Connected to notification stream');
        break;
    }
  }, []);

  const sse = useSSE({
    endpoint: '/api/sse/notifications',
    onMessage: handleNotification,
    onConnect: () => console.log('🔔 Notification SSE connected'),
    onDisconnect: () => console.log('❌ Notification SSE disconnected'),
  });

  return {
    ...sse,
    notifications,
    unreadCount,
    setNotifications,
    setUnreadCount
  };
}
