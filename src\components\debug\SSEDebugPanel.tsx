"use client";

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useSSEMessages, useSSENotifications } from '@/hooks/useSSE';
import { Button } from '@/components/ui/Button';
import { runSSEDiagnostics, sseDiagnostics } from '@/lib/utils/sseDiagnostics';

export function SSEDebugPanel() {
  const { data: session } = useSession();
  const [isVisible, setIsVisible] = useState(false);
  const [testMessage, setTestMessage] = useState('');
  const [diagnosticsRunning, setDiagnosticsRunning] = useState(false);

  const messageSSE = useSSEMessages();
  const notificationSSE = useSSENotifications();

  // Only show in development
  useEffect(() => {
    setIsVisible(process.env.NODE_ENV === 'development');
  }, []);

  const runDiagnostics = async () => {
    setDiagnosticsRunning(true);
    try {
      await runSSEDiagnostics();
    } catch (error) {
      console.error('Error running diagnostics:', error);
    } finally {
      setDiagnosticsRunning(false);
    }
  };

  const sendTestMessage = async () => {
    if (!testMessage.trim() || !session?.user) return;

    try {
      const response = await fetch('/api/messages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          receiverId: session.user.id, // Send to self for testing
          content: `[TEST] ${testMessage}`,
        }),
      });

      if (response.ok) {
        setTestMessage('');
        console.log('✅ Test message sent');
      }
    } catch (error) {
      console.error('❌ Error sending test message:', error);
    }
  };

  const testTyping = async () => {
    try {
      await fetch('/api/sse/typing', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'start',
          conversationId: session?.user?.id || 'test',
        }),
      });
      console.log('✅ Test typing indicator sent');
    } catch (error) {
      console.error('❌ Error testing typing:', error);
    }
  };

  if (!isVisible || !session?.user) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg shadow-lg p-4 max-w-sm z-50">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-semibold text-gray-900">SSE Debug Panel</h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-400 hover:text-gray-600"
        >
          ×
        </button>
      </div>

      <div className="space-y-3 text-xs">
        {/* Connection Status */}
        <div className="grid grid-cols-2 gap-2">
          <div className="flex items-center space-x-2">
            <div className={`w-2 h-2 rounded-full ${messageSSE.isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
            <span>Messages</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className={`w-2 h-2 rounded-full ${notificationSSE.isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
            <span>Notifications</span>
          </div>
        </div>

        {/* Connection States */}
        <div className="text-xs text-gray-600">
          <div>Messages: {messageSSE.connectionState}</div>
          <div>Notifications: {notificationSSE.connectionState}</div>
        </div>

        {/* Message Counts */}
        <div className="text-xs text-gray-600">
          <div>SSE Messages: {messageSSE.messages.length}</div>
          <div>SSE Notifications: {notificationSSE.notifications.length}</div>
          <div>Unread Count: {notificationSSE.unreadCount}</div>
        </div>

        {/* Test Controls */}
        <div className="space-y-2">
          <div className="flex space-x-1">
            <input
              type="text"
              value={testMessage}
              onChange={(e) => setTestMessage(e.target.value)}
              placeholder="Test message..."
              className="flex-1 px-2 py-1 text-xs border border-gray-300 rounded"
            />
            <Button
              onClick={sendTestMessage}
              size="sm"
              className="text-xs px-2 py-1"
            >
              Send
            </Button>
          </div>

          <div className="flex space-x-1">
            <Button
              onClick={testTyping}
              size="sm"
              variant="outline"
              className="text-xs px-2 py-1 flex-1"
            >
              Test Typing
            </Button>
            <Button
              onClick={() => {
                messageSSE.reconnect();
                notificationSSE.reconnect();
              }}
              size="sm"
              variant="outline"
              className="text-xs px-2 py-1 flex-1"
            >
              Reconnect
            </Button>
          </div>

          {/* Diagnostics Button */}
          <div className="mt-2">
            <Button
              onClick={runDiagnostics}
              disabled={diagnosticsRunning}
              size="sm"
              variant="outline"
              className="text-xs px-2 py-1 w-full"
            >
              {diagnosticsRunning ? 'Running...' : '🔍 Run Diagnostics'}
            </Button>
          </div>
        </div>

        {/* Recent Events */}
        <div className="max-h-20 overflow-y-auto text-xs text-gray-500">
          <div className="font-medium mb-1">Recent Events:</div>
          {messageSSE.messages.slice(-3).map((msg, i) => (
            <div key={i} className="truncate">
              📨 {msg.content?.substring(0, 20)}...
            </div>
          ))}
          {notificationSSE.notifications.slice(0, 2).map((notif, i) => (
            <div key={i} className="truncate">
              🔔 {notif.type}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

// Toggle button for debug panel
export function SSEDebugToggle() {
  const [showPanel, setShowPanel] = useState(false);

  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <>
      <button
        onClick={() => setShowPanel(!showPanel)}
        className="fixed bottom-4 left-4 bg-blue-600 text-white px-3 py-2 rounded-lg text-xs font-medium shadow-lg hover:bg-blue-700 z-50"
      >
        SSE Debug
      </button>
      {showPanel && <SSEDebugPanel />}
    </>
  );
}
