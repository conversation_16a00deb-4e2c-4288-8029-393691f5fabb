"use client";

import { useEffect, useState } from 'react';
import { useSSE } from '@/hooks/useSSE';

interface SSETypingIndicatorProps {
  conversationId: string;
  currentUserId: string;
  className?: string;
}

export function SSETypingIndicator({ 
  conversationId, 
  currentUserId, 
  className = "" 
}: SSETypingIndicatorProps) {
  const [typingUsers, setTypingUsers] = useState<string[]>([]);

  const handleTypingMessage = (message: any) => {
    if (message.type === 'typing_update' && message.conversationId === conversationId) {
      // Filter out current user from typing users
      const otherTypingUsers = (message.typingUsers || []).filter(
        (userId: string) => userId !== currentUserId
      );
      setTypingUsers(otherTypingUsers);
    }
  };

  const { isConnected } = useSSE({
    endpoint: '/api/sse/typing',
    onMessage: handleTypingMessage,
    onConnect: () => console.log('🔗 Typing indicator SSE connected'),
    onDisconnect: () => console.log('❌ Typing indicator SSE disconnected'),
  });

  // Clear typing users when disconnected
  useEffect(() => {
    if (!isConnected) {
      setTypingUsers([]);
    }
  }, [isConnected]);

  if (typingUsers.length === 0) {
    return null;
  }

  return (
    <div className={`flex items-center space-x-1 text-sm text-gray-500 ${className}`}>
      <div className="flex space-x-1">
        <div className="flex space-x-1">
          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
        </div>
      </div>
      <span className="text-xs">
        {typingUsers.length === 1 
          ? 'Someone is typing...' 
          : `${typingUsers.length} people are typing...`
        }
      </span>
    </div>
  );
}

// Simple typing indicator for inline use
export function SimpleSSETypingIndicator({ 
  conversationId, 
  currentUserId 
}: { 
  conversationId: string; 
  currentUserId: string; 
}) {
  const [isTyping, setIsTyping] = useState(false);

  const handleTypingMessage = (message: any) => {
    if (message.type === 'typing_update' && message.conversationId === conversationId) {
      const otherTypingUsers = (message.typingUsers || []).filter(
        (userId: string) => userId !== currentUserId
      );
      setIsTyping(otherTypingUsers.length > 0);
    }
  };

  const { isConnected } = useSSE({
    endpoint: '/api/sse/typing',
    onMessage: handleTypingMessage,
  });

  useEffect(() => {
    if (!isConnected) {
      setIsTyping(false);
    }
  }, [isConnected]);

  if (!isTyping) {
    return null;
  }

  return (
    <div className="flex items-center space-x-1 text-xs text-blue-600 font-medium animate-pulse">
      <span>Typing...</span>
    </div>
  );
}
